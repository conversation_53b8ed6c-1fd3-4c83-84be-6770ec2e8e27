import { z } from 'zod';
import type { Context, TRPCProcedure } from '../../../types';

const InputSchema = z
  .object({
    commentId: z.string().uuid(),
  })
  .strict();

type Input = z.infer<typeof InputSchema>;
type MutationArgs = { input: Input; ctx: Context };

export const deleteInquiryCommentMutation = async ({
  input: { commentId },
  ctx: { prisma, asyncTx, userId, tenantId },
}: MutationArgs) => {
  const existingComment = await prisma.inquiryComment.findFirst({
    where: {
      id: commentId,
      tenantId,
      deleted: false,
      originalId: null,
    },
  });

  if (!existingComment) {
    throw new Error('Comment not found');
  }

  if (existingComment.userId !== userId) {
    throw new Error('Permission denied: You can only delete your own comments');
  }

  await asyncTx(async (tx) => {
    // Soft delete the comment
    await tx.inquiryComment.update({
      where: { id: commentId },
      data: { deleted: true },
    });

    // Soft delete all associated images
    await tx.inquiryCommentImage.updateMany({
      where: {
        commentId,
        deleted: false,
      },
      data: { deleted: true },
    });
  });

  return 'OK';
};

export const deleteInquiryComment = (p: TRPCProcedure) =>
  p.input(InputSchema).mutation(deleteInquiryCommentMutation);
