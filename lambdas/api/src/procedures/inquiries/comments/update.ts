import { ImageInputSchema } from 'common';
import { z } from 'zod';
import type { Prisma } from '../../../../../.prisma';
import type { Context, TRPCProcedure } from '../../../types';

const InputSchema = z
  .object({
    commentId: z.string().uuid(),
    body: z.string().min(1).max(1000),
    images: z.array(ImageInputSchema),
  })
  .strict();

type Input = z.infer<typeof InputSchema>;
type MutationArgs = { input: Input; ctx: Context };

export const updateInquiryCommentMutation = async ({
  input: { commentId, body, images },
  ctx: { prisma, asyncTx, tenantId },
}: MutationArgs) => {
  const exists = await prisma.inquiryComment.findFirst({
    where: {
      id: commentId,
      tenantId,
      deleted: false,
      originalId: null,
    },
    include: {
      images: {
        where: { deleted: false },
      },
    },
  });
  if (!exists) throw new Error('Comment not found');

  await asyncTx(async (tx) => {
    const commonData = {
      body,
      inquiry: { connect: { id: exists.inquiryId } },
      user: { connect: { id: exists.userId } },
    };

    const updateData: Prisma.InquiryCommentUpdateInput = {
      ...commonData,
      images: { deleteMany: {}, createMany: { data: images } },
    };

    const versionData: Prisma.InquiryCommentCreateInput = {
      ...commonData,
      images: { createMany: { data: images } },
    };

    await tx.inquiryComment.update({ data: updateData, where: { id: commentId } });

    // Create version with originalId pointing to the original comment
    await tx.inquiryComment.create({
      data: {
        ...versionData,
        original: { connect: { id: commentId } },
      },
    });
  });

  return 'OK';
};

export const updateInquiryComment = (p: TRPCProcedure) =>
  p.input(InputSchema).mutation(updateInquiryCommentMutation);
